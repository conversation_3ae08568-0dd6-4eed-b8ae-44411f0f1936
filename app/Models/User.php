<?php

namespace App\Models;

use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    use HasApiTokens, HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'tokens'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'tokens' => 'integer',
    ];

    /**
     * Get the activities associated with the user.
     */
    public function activities()
    {
        return $this->hasMany(Activity::class);
    }

    /**
     * Get the exported activities associated with the user.
     */
    public function exportedActivities()
    {
        return $this->hasMany(ExportedActivity::class);
    }

    /**
     * Determine if user has available tokens
     *
     * @return bool
     */
    public function hasAvailableTokens()
    {
        return $this->tokens > 0;
    }

    /**
     * Decrement user tokens with logging and safeguards
     *
     * @return bool
     */
    public function useToken()
    {
        if ($this->hasAvailableTokens()) {
            $originalTokens = $this->tokens;
            $this->tokens -= 1;

            // Log token usage for audit trail
            \Log::info('Token used', [
                'user_id' => $this->id,
                'user_email' => $this->email,
                'tokens_before' => $originalTokens,
                'tokens_after' => $this->tokens,
                'timestamp' => now()->toISOString(),
                'database' => \DB::connection()->getDatabaseName()
            ]);

            return $this->save();
        }

        // Log failed token usage attempt
        \Log::warning('Token usage failed - no tokens available', [
            'user_id' => $this->id,
            'user_email' => $this->email,
            'current_tokens' => $this->tokens,
            'timestamp' => now()->toISOString()
        ]);

        return false;
    }

    /**
     * Override delete to prevent accidental user deletion
     */
    public function delete()
    {
        // Log deletion attempt
        \Log::emergency('USER DELETION ATTEMPT', [
            'user_id' => $this->id,
            'user_email' => $this->email,
            'user_name' => $this->name,
            'tokens' => $this->tokens,
            'created_at' => $this->created_at,
            'stack_trace' => debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS),
            'timestamp' => now()->toISOString(),
            'database' => \DB::connection()->getDatabaseName()
        ]);

        // In production, require explicit confirmation
        if (app()->environment('production')) {
            throw new \Exception('User deletion is not allowed in production without explicit confirmation. Use forceDelete() if intentional.');
        }

        return parent::delete();
    }

    /**
     * Force delete with explicit confirmation (for admin use only)
     */
    public function forceDeleteUser($confirmation = null)
    {
        if ($confirmation !== 'CONFIRM_DELETE_USER_' . $this->id) {
            throw new \Exception('Invalid confirmation token for user deletion');
        }

        \Log::emergency('FORCED USER DELETION', [
            'user_id' => $this->id,
            'user_email' => $this->email,
            'confirmation' => $confirmation,
            'timestamp' => now()->toISOString()
        ]);

        return parent::delete();
    }
}
