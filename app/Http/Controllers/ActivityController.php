<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class ActivityController extends Controller
{
    /**
     * Get all activities for the authenticated user
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function index()
    {
        $activities = Auth::user()->activities()->latest()->get();
        return response()->json($activities);
    }

    /**
     * Store a newly created activity in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(Request $request)
    {
        $user = Auth::user();

        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'route_points' => 'required|array',
            'activity_date' => 'required|date',
            'activity_time' => 'required|string',
            'duration' => 'required|numeric',
            'pace' => 'required|numeric',
            'activity_type' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        // Create activity without using a token
        $validated = $validator->validated();
        $activity = $user->activities()->create($validated);

        return response()->json([
            'message' => 'Activity created successfully',
            'activity' => $activity
        ], 201);
    }

    /**
     * Display the specified activity.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($id)
    {
        $activity = Auth::user()->activities()->findOrFail($id);
        return response()->json($activity);
    }

    /**
     * Update the specified activity in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(Request $request, $id)
    {
        $activity = Auth::user()->activities()->findOrFail($id);

        $validator = Validator::make($request->all(), [
            'name' => 'string|max:255',
            'route_points' => 'array',
            'activity_date' => 'date',
            'activity_time' => 'string',
            'duration' => 'numeric',
            'pace' => 'numeric',
            'activity_type' => 'string',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $activity->update($request->all());

        return response()->json([
            'message' => 'Activity updated successfully',
            'activity' => $activity
        ]);
    }

    /**
     * Remove the specified activity from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\JsonResponse
     */
    public function destroy($id)
    {
        $activity = Auth::user()->activities()->findOrFail($id);
        $activity->delete();

        return response()->json([
            'message' => 'Activity deleted successfully'
        ]);
    }
}
