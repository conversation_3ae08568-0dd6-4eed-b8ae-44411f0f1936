<?php

namespace App\Http\Controllers;

use App\Models\Activity;
use App\Models\ExportedActivity;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use phpGPX\Models\Extensions;
use phpGPX\Models\Extensions\TrackPointExtension;
use phpGPX\Models\GpxFile;
use phpGPX\Models\Link;
use phpGPX\Models\Metadata;
use phpGPX\Models\Point;
use phpGPX\Models\Segment;
use phpGPX\Models\Track;
use phpGPX\phpGPX;

class ActivityExportController extends Controller
{
    /**
     * ISO 8601 datetime format constant for GPX files
     */
    private const GPX_DATETIME_FORMAT = 'Y-m-d\TH:i:s\Z';

    /**
     * Generate GPX file from activity data
     *
     * @param Request $request
     * @return Response
     */
    public function generateGpx(Request $request)
    {
        // Check if user is authenticated
        $user = Auth::user();

        // Check if user has available tokens (only for authenticated users)
        if ($user && !$user->hasAvailableTokens()) {
            return response()->json([
                'message' => 'You do not have enough tokens to export this activity.',
                'tokens_available' => 0
            ], 403);
        }

        // Validate incoming request
        $validated = $request->validate([
            'activity_id' => 'nullable|exists:activities,id',
            'name' => 'required|string',
            'routePoints' => 'present|array',
            'activityDate' => 'required|string',
            'activityTime' => 'required|string',
            'duration' => 'required|numeric',
            'pace' => 'required|numeric',
            'includeHeartRate' => 'boolean',
            'includeCadence' => 'boolean',
            'includeElevation' => 'boolean',
            'heartRate' => 'nullable|numeric',
            'cadence' => 'nullable|numeric',
            'distance' => 'nullable|numeric', // Add distance validation
            'timezone' => 'nullable|string', // Optional timezone parameter
        ]);

        try {

            // Format the route points using phpGPX library
            $formattedPoints = $this->formatRoutePoints(
                $validated['routePoints'],
                $validated['activityDate'],
                $validated['activityTime'],
                $validated['duration'],
                $validated['includeHeartRate'] ?? false,
                $validated['includeElevation'] ?? false,
                $validated['heartRate'] ?? null,
                $validated['includeCadence'] ?? false,
                $validated['cadence'] ?? null,
                $validated['timezone'] ?? null
            );
            $formattedPace = $this->formatPaceForGpx($validated['pace']);
            $displayPace = Activity::formatPaceDisplay($validated['pace']);

            // Generate filename
            $filename = $this->sanitizeFileName($validated['name']) . '.gpx';

            // Generate GPX content using phpGPX library
            $gpxContent = $this->createGpxContent(
                $validated['name'],
                $formattedPoints,
                $formattedPace,
                $validated['distance'] ?? null
            );

            // For authenticated users, log the export and use a token
            if ($user) {
                // Use a token
                $user->useToken();

                // Get activity ID from request or find/create the activity
                $activityId = $validated['activity_id'] ?? null;

                if (!$activityId) {

                    $activity = Activity::create([
                        'user_id' => $user->id,
                        'name' => $validated['name'],
                        'route_points' => $validated['routePoints'],
                        'activity_date' => $validated['activityDate'],
                        'activity_time' => $validated['activityTime'],
                        'duration' => $validated['duration'],
                        'pace' => $formattedPace,
                        'distance' => $validated['distance'] ?? null,
                        'activity_type' => 'run'
                    ]);
                    $activityId = $activity->id;
                }

                // Save exported activity data
                $exportedActivity = ExportedActivity::create([
                    'user_id' => $user->id,
                    'activity_id' => $activityId,
                    'format' => 'gpx',
                    'file_name' => $filename,
                    'export_data' => $gpxContent
                ]);

                // Return the GPX content with token information
                return response()->json([
                    'success' => true,
                    'filename' => $filename,
                    'content' => $gpxContent,
                    'mimeType' => 'application/gpx+xml',
                    'tokens_remaining' => $user->tokens,
                    'pace_display' => $displayPace
                ]);
            }

            // For non-authenticated users, just return the GPX content
            return response()->json([
                'success' => true,
                'filename' => $filename,
                'content' => $gpxContent,
                'mimeType' => 'application/gpx+xml',
                'pace_display' => $displayPace
            ]);
        } catch (\Exception $e) {
            return response()->json(['error' => $e->getMessage()], 400);
        }
    }

    /**
     * Create GPX content using phpGPX library
     *
     * @param string $name Activity name
     * @param array $points Formatted route points
     * @param float $pace Pace in minutes per kilometer
     * @param float|null $distance Distance in meters (optional)
     * @return string GPX file content
     */
    private function createGpxContent(string $name, array $points, float $pace, ?float $distance = null): string
    {
        // Configure phpGPX
        phpGPX::$PRETTY_PRINT = true;
        phpGPX::$DATETIME_FORMAT = 'Y-m-d\TH:i:s\Z'; // Ensure consistent datetime format
        phpGPX::$DATETIME_TIMEZONE_OUTPUT = 'UTC';
        phpGPX::$CALCULATE_STATS = true; // Enable stats calculation
        phpGPX::$APPLY_ELEVATION_SMOOTHING = true;
        phpGPX::$ELEVATION_SMOOTHING_THRESHOLD = 2;
        phpGPX::$ELEVATION_SMOOTHING_SPIKES_THRESHOLD = null;

        // Create new GPX file
        $gpxFile = new GpxFile();

        // Set up metadata
        $gpxFile->metadata = new Metadata();
        $gpxFile->metadata->time = new \DateTime();
        $gpxFile->metadata->description = "Activity exported from Garmin Connect using Create My Run";

        // Create a link to the app
        $link = new Link();
        $link->href = "https://create-my.run/";
        $link->text = "Create My Run";
        $gpxFile->metadata->links[] = $link;

        // Create a new track
        $track = new Track();
        $track->name = $name;
        $track->type = 'running';
        $track->source = 'Garmin';
        $track->stats = new \phpGPX\Models\Stats();
        $track->stats->averagePace = $pace;

        // Create a track segment
        $segment = new Segment();

        // Add points to segment
        foreach ($points as $index => $pointData) {
            $point = new Point(Point::TRACKPOINT);
            $point->latitude = $pointData['lat'];
            $point->longitude = $pointData['lng'];

            // Set elevation if available
            if (isset($pointData['elevation'])) {
                $point->elevation = $pointData['elevation'];
            }

            // Convert string time to DateTime object
            if (isset($pointData['time'])) {
                $point->time = new \DateTime($pointData['time']);
            }

            // Add heart rate extension if available
            if (isset($pointData['heartRate'])) {
                $extensions = new \phpGPX\Models\Extensions();
                $trackPointExt = new \phpGPX\Models\Extensions\TrackPointExtension();
                $trackPointExt->hr = $pointData['heartRate'];
                $extensions->trackPointExtension = $trackPointExt;
                $point->extensions = $extensions;
            }

            // Add cadence extension if available
            if (isset($pointData['cadence'])) {
                if (!isset($point->extensions)) {
                    $point->extensions = new \phpGPX\Models\Extensions();
                    $point->extensions->trackPointExtension = new \phpGPX\Models\Extensions\TrackPointExtension();
                }
                $point->extensions->trackPointExtension->cad = $pointData['cadence'];
            }

            // Add speed extension based on pace if available
            if ($pace > 0) {
                // Convert pace (min/km) to speed (m/s)
                // Formula: speed = distance / time = 1000m / (pace_in_minutes * 60s)
                $baseSpeedMps = 1000.0 / ($pace * 60.0);

                // Add realistic speed variation (±5% to simulate natural pace variation)
                $variationPercent = (mt_rand(-5, 5) / 100.0); // -5% to +5%
                $speedMps = $baseSpeedMps * (1 + $variationPercent);

                if (!isset($point->extensions)) {
                    $point->extensions = new \phpGPX\Models\Extensions();
                    $point->extensions->trackPointExtension = new \phpGPX\Models\Extensions\TrackPointExtension();
                }

                // Set speed extension (some GPX readers use this for moving time calculation)
                // Ensure speed is reasonable (0.5 m/s to 10 m/s for running)
                $speedMps = max(0.5, min(10.0, $speedMps));
                $point->extensions->trackPointExtension->speed = $speedMps;
            }

            $segment->points[] = $point;
        }

        // Add segment to track
        $track->segments[] = $segment;

        // If distance is provided, set it in the track stats
        if ($distance) {
            if (!$track->stats) {
                $track->stats = new \phpGPX\Models\Stats();
            }
            $track->stats->distance = $distance;
        }

        // Calculate track statistics
        $track->recalculateStats();

        // Add track to file
        $gpxFile->tracks[] = $track;

        // Generate XML content
        return $gpxFile->toXML()->saveXML();
    }

    /**
     * Format route points from frontend format to phpGPX format
     *
     * @param array $routePoints
     * @param string $date
     * @param string $time
     * @param float $duration
     * @param bool $includeHeartRate
     * @param bool $includeElevation
     * @param int|null $heartRate
     * @param bool $includeCadence
     * @param int|null $cadence
     * @param string|null $timezone
     * @return array
     */
    private function formatRoutePoints(array $routePoints, string $date, string $time, float $duration, bool $includeHeartRate = false, bool $includeElevation = false, ?int $heartRate = null, bool $includeCadence = false, ?int $cadence = null, ?string $timezone = null): array
    {
        $formattedPoints = [];

        // Handle timezone conversion properly
        if ($timezone) {
            // If timezone is provided, create DateTime object with that timezone and convert to UTC
            try {
                $dateTime = new \DateTime("$date $time", new \DateTimeZone($timezone));
                $dateTime->setTimezone(new \DateTimeZone('UTC'));
                $startTimeUtc = $dateTime->getTimestamp();
            } catch (\Exception $e) {
                // Fallback to treating as local time if timezone is invalid
                $startTimeUtc = strtotime("$date $time");
            }
        } else {
            // Treat the input time as local time (not UTC) to preserve user's intended time
            // This prevents timezone conversion issues when the GPX is read by Strava
            $startTimeUtc = strtotime("$date $time");
        }

        $pointCount = count($routePoints);

        if ($pointCount < 1) {
            return $formattedPoints;
        }

        // Calculate total distance and segment distances
        $totalDistance = 0;
        $segmentDistances = [];
        $cumulativeDistances = [0]; // First point is at distance 0

        for ($i = 1; $i < $pointCount; $i++) {
            $prevLat = $routePoints[$i-1][0];
            $prevLng = $routePoints[$i-1][1];
            $currentLat = $routePoints[$i][0];
            $currentLng = $routePoints[$i][1];

            // Calculate distance between consecutive points using Haversine formula
            $segmentDistance = $this->calculateDistance($prevLat, $prevLng, $currentLat, $currentLng);
            $segmentDistances[] = $segmentDistance;

            $totalDistance += $segmentDistance;
            $cumulativeDistances[] = $totalDistance;
        }

        // Calculate timestamps for each point based on pace and distance
        $endTimeUtc = $startTimeUtc + $duration;
        $totalTimeSpan = $endTimeUtc - $startTimeUtc;

        // Generate more frequent track points for better Strava compatibility
        $allFormattedPoints = [];

        foreach ($routePoints as $index => $point) {
            // Calculate proportional time based on distance covered
            $distanceRatio = ($totalDistance > 0) ? $cumulativeDistances[$index] / $totalDistance : 0;
            $pointTime = $startTimeUtc + ($distanceRatio * $totalTimeSpan);

            // For elevation, use realistic values (0-2m) with decimal precision
            $elevation = null;
            if ($includeElevation) {
                $elevation = mt_rand(0, 20) / 10; // 0.0 to 2.0m with one decimal place
            }

            $formattedPoint = [
                'lat' => $point[0],
                'lng' => $point[1],
                'time' => gmdate(self::GPX_DATETIME_FORMAT, $pointTime)
            ];

            // Add elevation if requested
            if ($elevation !== null) {
                $formattedPoint['elevation'] = $elevation;
            }

            // Add heart rate if requested
            if ($includeHeartRate && $heartRate) {
                $formattedPoint['heartRate'] = $heartRate;
            }

            // Add cadence if requested
            if ($includeCadence && $cadence) {
                $formattedPoint['cadence'] = $cadence;
            }

            $allFormattedPoints[] = $formattedPoint;

            // Add intermediate points between this point and the next (except for the last point)
            if ($index < count($routePoints) - 1) {
                $nextPoint = $routePoints[$index + 1];
                $nextDistanceRatio = ($totalDistance > 0) ? $cumulativeDistances[$index + 1] / $totalDistance : 0;
                $nextPointTime = $startTimeUtc + ($nextDistanceRatio * $totalTimeSpan);

                // Calculate time gap between current and next point
                $timeGap = $nextPointTime - $pointTime;

                // If gap is larger than 30 seconds, add intermediate points every 15-30 seconds
                if ($timeGap > 30) {
                    $intermediatePointsCount = min(floor($timeGap / 20), 10); // Max 10 intermediate points

                    for ($i = 1; $i <= $intermediatePointsCount; $i++) {
                        $ratio = $i / ($intermediatePointsCount + 1);

                        // Interpolate position
                        $intermediateLat = $point[0] + ($nextPoint[0] - $point[0]) * $ratio;
                        $intermediateLng = $point[1] + ($nextPoint[1] - $point[1]) * $ratio;

                        // Interpolate time
                        $intermediateTime = $pointTime + ($timeGap * $ratio);

                        // Interpolate elevation if needed
                        $intermediateElevation = null;
                        if ($includeElevation) {
                            $intermediateElevation = mt_rand(0, 20) / 10;
                        }

                        $intermediatePoint = [
                            'lat' => $intermediateLat,
                            'lng' => $intermediateLng,
                            'time' => gmdate(self::GPX_DATETIME_FORMAT, $intermediateTime)
                        ];

                        // Add elevation if requested
                        if ($intermediateElevation !== null) {
                            $intermediatePoint['elevation'] = $intermediateElevation;
                        }

                        // Add heart rate if requested
                        if ($includeHeartRate && $heartRate) {
                            $intermediatePoint['heartRate'] = $heartRate;
                        }

                        // Add cadence if requested
                        if ($includeCadence && $cadence) {
                            $intermediatePoint['cadence'] = $cadence;
                        }

                        $allFormattedPoints[] = $intermediatePoint;
                    }
                }
            }
        }

        // Process all formatted points and ensure they have required data
        foreach ($allFormattedPoints as $formattedPoint) {
            // Always include elevation as Strava expects it, use existing or generate new
            if (!isset($formattedPoint['elevation'])) {
                $formattedPoint['elevation'] = mt_rand(0, 20) / 10; // 0.0 to 2.0m
            }

            $formattedPoints[] = $formattedPoint;
        }

        return $formattedPoints;
    }

    /**
     * Calculate distance between two points using Haversine formula
     *
     * @param float $lat1 Latitude of point 1 in degrees
     * @param float $lng1 Longitude of point 1 in degrees
     * @param float $lat2 Latitude of point 2 in degrees
     * @param float $lng2 Longitude of point 2 in degrees
     * @return float Distance in meters
     */
    private function calculateDistance(float $lat1, float $lng1, float $lat2, float $lng2): float
    {
        // Earth's radius in meters
        $earthRadius = 6371000;

        // Convert degrees to radians
        $lat1Rad = deg2rad($lat1);
        $lng1Rad = deg2rad($lng1);
        $lat2Rad = deg2rad($lat2);
        $lng2Rad = deg2rad($lng2);

        // Calculate differences
        $latDelta = $lat2Rad - $lat1Rad;
        $lngDelta = $lng2Rad - $lng1Rad;

        // Haversine formula
        $a = sin($latDelta/2) * sin($latDelta/2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($lngDelta/2) * sin($lngDelta/2);
        $c = 2 * atan2(sqrt($a), sqrt(1-$a));

        // Distance in meters
        return $earthRadius * $c;
    }

    /**
     * Format pace to the correct format for GPX files
     *
     * @param float $pace Pace in minutes per kilometer
     * @return float Pace in minutes per kilometer (for internal use)
     */
    private function formatPaceForGpx(float $pace): float
    {
        // Validate pace input
        if ($pace <= 0) {
            $pace = 5.0; // Default to 5 min/km if invalid
        }

        // Apply realistic limits (world record is ~2:50 min/km, typical slowest is ~15 min/km)
        $minPace = 2.83; // 2:50 min/km (elite runner pace)
        $maxPace = 15.0; // 15:00 min/km (very slow walking pace)

        $pace = min(max($pace, $minPace), $maxPace);

        return $pace;
    }

    /**
     * Sanitize filename
     *
     * @param string $name
     * @return string
     */
    private function sanitizeFileName(string $name): string
    {
        return preg_replace('/[^a-z0-9]/i', '_', $name);
    }
}

