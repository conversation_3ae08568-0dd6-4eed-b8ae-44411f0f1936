<?php

use App\Http\Controllers\ActivityExportController;
use App\Models\Activity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\Request;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->controller = new ActivityExportController();
    $this->user = User::factory()->withManyTokens()->create();

    $this->sampleRoutePoints = [
        [37.7749, -122.4194],
        [37.7750, -122.4195],
        [37.7751, -122.4196],
        [37.7752, -122.4197]
    ];

    $this->validRequestData = [
        'name' => 'Test Run',
        'routePoints' => $this->sampleRoutePoints,
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 1800,
        'pace' => 5.5,
        'distance' => 5000,
        'includeHeartRate' => true,
        'heartRate' => 150,
        'includeElevation' => true,
        'includeCadence' => true,
        'cadence' => 180
    ];
});

// GPX XML Structure Validation Tests

it('generates valid XML structure', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    // Test XML validity
    $xml = simplexml_load_string($content);
    expect($xml)->not->toBeFalse();

    // Test XML declaration
    expect($content)->toStartWith('<?xml version="1.0"');

    // Test root element
    expect($content)->toContain('<gpx');
    expect($content)->toContain('</gpx>');
});

it('includes required GPX namespaces', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('xmlns="http://www.topografix.com/GPX/1/1"');
    expect($content)->toContain('xmlns:gpxtpx="http://www.garmin.com/xmlschemas/TrackPointExtension/v2"');
    expect($content)->toContain('version="1.1"');
});

it('includes metadata section', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<metadata>');
    expect($content)->toContain('<time>');
    expect($content)->toContain('<desc>Activity exported from Garmin Connect using Create My Run</desc>');
    expect($content)->toContain('<link href="https://create-my.run/">');
    expect($content)->toContain('<text>Create My Run</text>');
});

it('includes track structure', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<trk>');
    expect($content)->toContain('<name>Test Run</name>');
    expect($content)->toContain('<type>running</type>');
    expect($content)->toContain('<trkseg>');
    expect($content)->toContain('<trkpt');
});

// Track Point Generation Tests

it('generates correct number of track points', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    $xml = simplexml_load_string($content);
    expect($xml)->not->toBeFalse();

    // Register the GPX namespace for XPath queries
    $xml->registerXPathNamespace('gpx', 'http://www.topografix.com/GPX/1/1');
    $trackPoints = $xml->xpath('//gpx:trkpt');

    $inputPointCount = count($this->sampleRoutePoints);
    $outputPointCount = count($trackPoints);

    // Should have more points than input due to interpolation
    expect($outputPointCount)->toBeGreaterThan($inputPointCount);
});

it('includes latitude and longitude attributes', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('lat="37.7749"');
    expect($content)->toContain('lon="-122.4194"');
});

it('includes elevation data when requested', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<ele>');

    $xml = simplexml_load_string($content);
    $xml->registerXPathNamespace('gpx', 'http://www.topografix.com/GPX/1/1');
    $elevations = $xml->xpath('//gpx:ele');
    expect(count($elevations))->toBeGreaterThan(0);
});

it('includes timestamp data', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<time>');

    // Check ISO 8601 format - accept both Z and +00:00 timezone formats
    expect($content)->toMatch('/<time>\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(Z|\+00:00)<\/time>/');
});

it('includes heart rate extensions when requested', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<gpxtpx:hr>150</gpxtpx:hr>');
});

it('includes cadence extensions when requested', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<gpxtpx:cad>180</gpxtpx:cad>');
});

it('includes speed extensions', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('<gpxtpx:speed>');
});

it('excludes optional data when not requested', function () {
    $minimalData = [
        'name' => 'Test Run',
        'routePoints' => $this->sampleRoutePoints,
        'activityDate' => '2024-01-15',
        'activityTime' => '08:30:00',
        'duration' => 1800,
        'pace' => 5.5,
        'includeHeartRate' => false,
        'includeElevation' => false,
        'includeCadence' => false
    ];

    $request = Request::create('/api/export/gpx', 'POST', $minimalData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    // Should still include elevation (always added)
    expect($content)->toContain('<ele>');
    // But not heart rate or cadence
    expect($content)->not->toContain('<gpxtpx:hr>');
    expect($content)->not->toContain('<gpxtpx:cad>');
});

// Coordinate Accuracy Tests

it('preserves coordinate precision', function () {
    $precisePoints = [
        [37.774929, -122.419416],
        [37.774930, -122.419417]
    ];

    $data = array_merge($this->validRequestData, ['routePoints' => $precisePoints]);
    $request = Request::create('/api/export/gpx', 'POST', $data);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('lat="37.774929"');
    expect($content)->toContain('lon="-122.419416"');
});

it('handles negative coordinates correctly', function () {
    $negativePoints = [
        [-37.774929, -122.419416],
        [-37.774930, -122.419417]
    ];

    $data = array_merge($this->validRequestData, ['routePoints' => $negativePoints]);
    $request = Request::create('/api/export/gpx', 'POST', $data);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('lat="-37.774929"');
    expect($content)->toContain('lon="-122.419416"');
});

it('handles edge case coordinates', function () {
    $edgePoints = [
        [0.0, 0.0],
        [90.0, 180.0],
        [-90.0, -180.0]
    ];

    $data = array_merge($this->validRequestData, ['routePoints' => $edgePoints]);
    $request = Request::create('/api/export/gpx', 'POST', $data);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    expect($content)->toContain('lat="0"');
    expect($content)->toContain('lon="0"');
    expect($content)->toContain('lat="90"');
    expect($content)->toContain('lon="180"');
    expect($content)->toContain('lat="-90"');
    expect($content)->toContain('lon="-180"');
});

// Timestamp Generation Tests

it('generates sequential timestamps', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    $xml = simplexml_load_string($content);
    $xml->registerXPathNamespace('gpx', 'http://www.topografix.com/GPX/1/1');

    // Only get track point times, not metadata times
    $times = $xml->xpath('//gpx:trkpt/gpx:time');

    // Convert to timestamps and verify they're sequential
    $timestamps = array_map(function($time) {
        return strtotime((string)$time);
    }, $times);

    for ($i = 1; $i < count($timestamps); $i++) {
        expect($timestamps[$i])->toBeGreaterThanOrEqual($timestamps[$i-1]);
    }
});

it('respects activity date and time', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    // Accept both Z and +00:00 timezone formats
    $hasCorrectTime = str_contains($content, '2024-01-15T08:30:00Z') ||
                      str_contains($content, '2024-01-15T08:30:00+00:00');
    expect($hasCorrectTime)->toBeTrue();
});

it('preserves user input datetime without timezone conversion', function () {
    // Test with a specific time that would be affected by timezone conversion
    $testData = array_merge($this->validRequestData, [
        'activityDate' => '2024-04-27',
        'activityTime' => '18:30', // 6:30 PM
        'duration' => 3600 // 1 hour
    ]);

    $request = Request::create('/api/export/gpx', 'POST', $testData);
    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    // Parse the XML to get the first track point time
    $xml = simplexml_load_string($content);
    $xml->registerXPathNamespace('gpx', 'http://www.topografix.com/GPX/1/1');
    $firstTrackPointTime = $xml->xpath('//gpx:trkpt/gpx:time')[0];

    // The first track point should start at exactly the user's input time
    // Accept both Z and +00:00 timezone formats as they are equivalent
    $timeString = (string)$firstTrackPointTime;
    $hasCorrectTime = $timeString === '2024-04-27T18:30:00Z' ||
                      $timeString === '2024-04-27T18:30:00+00:00';
    expect($hasCorrectTime)->toBeTrue();

    // Get the last track point time to verify duration
    $trackPointTimes = $xml->xpath('//gpx:trkpt/gpx:time');
    $lastTrackPointTime = end($trackPointTimes);

    // Calculate the time difference
    $startTimestamp = strtotime((string)$firstTrackPointTime);
    $endTimestamp = strtotime((string)$lastTrackPointTime);
    $actualDuration = $endTimestamp - $startTimestamp;

    // Should be close to 1 hour (3600 seconds)
    expect($actualDuration)->toBeBetween(3500, 3700);
});

it('distributes timestamps across duration', function () {
    $request = Request::create('/api/export/gpx', 'POST', $this->validRequestData);

    $response = $this->controller->generateGpx($request);
    $content = $response->getData()->content;

    $xml = simplexml_load_string($content);
    $xml->registerXPathNamespace('gpx', 'http://www.topografix.com/GPX/1/1');

    // Only get track point times, not metadata times
    $times = $xml->xpath('//gpx:trkpt/gpx:time');

    $firstTime = strtotime((string)$times[0]);
    $lastTime = strtotime((string)$times[count($times) - 1]);

    $actualDuration = $lastTime - $firstTime;

    // Should be close to the specified duration (1800 seconds)
    expect($actualDuration)->toBeBetween(1700, 1900);
});
