<?php

use App\Http\Controllers\ActivityExportController;
use Illuminate\Http\Request;

it('preserves user intended time without timezone conversion', function () {
    $controller = new ActivityExportController();
    
    // Test data - user wants 8:00 AM on May 28, 2025
    $requestData = [
        'name' => 'Morning Run',
        'routePoints' => [
            [40.7128, -74.0060], // New York coordinates
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);
    
    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();
    
    $gpxContent = $responseData->content;
    
    // The GPX should contain the time as 08:00 (or close to it) without timezone conversion
    // Look for the start time in the GPX content
    expect($gpxContent)->toContain('2025-05-28T08:');
});

it('handles timezone parameter correctly when provided', function () {
    $controller = new ActivityExportController();
    
    // Test data with timezone - user in EST wants 8:00 AM local time
    $requestData = [
        'name' => 'Morning Run with Timezone',
        'routePoints' => [
            [40.7128, -74.0060], // New York coordinates
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000,
        'timezone' => 'America/New_York' // EST/EDT timezone
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);
    
    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();
    
    $gpxContent = $responseData->content;
    
    // When timezone is provided, the time should be converted to UTC
    // 8:00 AM EST in May (EDT) = 12:00 PM UTC
    expect($gpxContent)->toContain('2025-05-28T12:');
});

it('handles invalid timezone gracefully', function () {
    $controller = new ActivityExportController();
    
    // Test data with invalid timezone
    $requestData = [
        'name' => 'Morning Run with Invalid Timezone',
        'routePoints' => [
            [40.7128, -74.0060],
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800,
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000,
        'timezone' => 'Invalid/Timezone'
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);
    
    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();
    
    // Should fallback to treating as local time
    $gpxContent = $responseData->content;
    expect($gpxContent)->toContain('2025-05-28T08:');
});
