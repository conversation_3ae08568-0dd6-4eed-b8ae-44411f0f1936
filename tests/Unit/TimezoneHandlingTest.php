<?php

use App\Http\Controllers\ActivityExportController;
use Illuminate\Http\Request;

it('preserves user input time as UTC to prevent Strava conversion issues', function () {
    $controller = new ActivityExportController();

    // Test data - user wants 8:00 AM on May 28, 2025 (without timezone specified)
    $requestData = [
        'name' => 'Morning Run',
        'routePoints' => [
            [40.7128, -74.0060], // New York coordinates
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    $gpxContent = $responseData->content;

    // The GPX should contain the user's input time directly as UTC
    // This prevents Strava from applying incorrect timezone conversions
    expect($gpxContent)->toContain('2025-05-28T08:');
});

it('handles timezone parameter correctly when provided', function () {
    $controller = new ActivityExportController();

    // Test data with timezone - user in Vietnam (UTC+7) wants 8:00 AM local time
    $requestData = [
        'name' => 'Morning Run with Timezone',
        'routePoints' => [
            [16.0707, 108.2362], // Vietnam coordinates
            [16.0708, 108.2363]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800, // 30 minutes
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000,
        'timezone' => 'Asia/Ho_Chi_Minh' // UTC+7 timezone
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    $gpxContent = $responseData->content;

    // Even with timezone provided, we store the user's input time as UTC
    // This prevents Strava from applying incorrect timezone conversions
    expect($gpxContent)->toContain('2025-05-28T08:');
});

it('handles invalid timezone gracefully', function () {
    $controller = new ActivityExportController();

    // Test data with invalid timezone
    $requestData = [
        'name' => 'Morning Run with Invalid Timezone',
        'routePoints' => [
            [40.7128, -74.0060],
            [40.7130, -74.0062]
        ],
        'activityDate' => '2025-05-28',
        'activityTime' => '08:00',
        'duration' => 1800,
        'pace' => 5.0,
        'includeHeartRate' => false,
        'includeCadence' => false,
        'includeElevation' => false,
        'distance' => 5000,
        'timezone' => 'Invalid/Timezone'
    ];

    $request = Request::create('/api/export/gpx', 'POST', $requestData);
    $response = $controller->generateGpx($request);

    $responseData = $response->getData();
    expect($responseData->success)->toBeTrue();

    // Should fallback to storing user input time as UTC
    $gpxContent = $responseData->content;
    expect($gpxContent)->toContain('2025-05-28T08:');
});
