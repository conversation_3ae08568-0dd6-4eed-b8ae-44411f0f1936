<?php

use App\Models\Activity;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use <PERSON>vel\Sanctum\Sanctum;

uses(RefreshDatabase::class);

beforeEach(function () {
    $this->user = User::factory()->create();
    $this->otherUser = User::factory()->create();

    $this->validActivityData = [
        'name' => 'Morning Run',
        'route_points' => [
            [37.7749, -122.4194],
            [37.7750, -122.4195],
            [37.7751, -122.4196]
        ],
        'activity_date' => '2024-01-15',
        'activity_time' => '08:30:00',
        'duration' => 1800,
        'pace' => 5.5,
        'activity_type' => 'running'
    ];
});

// Index - Get All Activities Tests

it('returns user activities in descending order', function () {
    Sanctum::actingAs($this->user);

    $activities = Activity::factory()->count(3)->create(['user_id' => $this->user->id]);
    Activity::factory()->count(2)->create(['user_id' => $this->otherUser->id]); // Other user's activities

    $response = $this->getJson('/api/activities');

    $response->assertStatus(200)
        ->assertJsonCount(3)
        ->assertJsonStructure([
            '*' => [
                'id',
                'name',
                'route_points',
                'activity_date',
                'activity_time',
                'duration',
                'pace',
                'activity_type',
                'formatted_pace',
                'created_at',
                'updated_at'
            ]
        ]);

    // Verify only user's activities are returned
    $returnedIds = collect($response->json())->pluck('id')->toArray();
    $userActivityIds = $activities->pluck('id')->toArray();

    expect($returnedIds)->toEqual($userActivityIds);
});

it('returns empty array when user has no activities', function () {
    Sanctum::actingAs($this->user);

    $response = $this->getJson('/api/activities');

    $response->assertStatus(200)
        ->assertJsonCount(0);
});

it('requires authentication for index', function () {
    $response = $this->getJson('/api/activities');

    $response->assertStatus(401);
});

// Store - Create Activity Tests

it('creates activity with valid data', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/activities', $this->validActivityData);

    $response->assertStatus(201)
        ->assertJsonStructure([
            'message',
            'activity' => [
                'id',
                'name',
                'route_points',
                'activity_date',
                'activity_time',
                'duration',
                'pace',
                'activity_type',
                'user_id',
                'created_at',
                'updated_at'
            ]
        ]);

    $this->assertDatabaseHas('activities', [
        'name' => 'Morning Run',
        'user_id' => $this->user->id,
        'activity_type' => 'running'
    ]);
});

it('validates required fields', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/activities', []);

    $response->assertStatus(422)
        ->assertJsonValidationErrors([
            'name',
            'route_points',
            'activity_date',
            'activity_time',
            'duration',
            'pace'
        ]);
});

it('validates field types', function () {
    Sanctum::actingAs($this->user);

    $invalidData = [
        'name' => str_repeat('a', 256), // Too long
        'route_points' => 'invalid', // Should be array
        'activity_date' => 'invalid-date',
        'activity_time' => 'invalid-time',
        'duration' => 'invalid',
        'pace' => 'invalid'
    ];

    $response = $this->postJson('/api/activities', $invalidData);

    $response->assertStatus(422)
        ->assertJsonValidationErrors([
            'name',
            'route_points',
            'activity_date',
            'duration',
            'pace'
        ]);
});

it('stores route points as JSON', function () {
    Sanctum::actingAs($this->user);

    $response = $this->postJson('/api/activities', $this->validActivityData);

    $activity = Activity::latest()->first();
    expect($activity->route_points)->toBeArray();
    expect($activity->route_points)->toEqual($this->validActivityData['route_points']);
});

it('sets default activity type when not provided', function () {
    Sanctum::actingAs($this->user);

    $dataWithoutType = $this->validActivityData;
    unset($dataWithoutType['activity_type']);

    $response = $this->postJson('/api/activities', $dataWithoutType);

    $response->assertStatus(201);

    $activity = Activity::latest()->first();
    expect($activity->activity_type)->toBe('run'); // Database default value
});

it('requires authentication for store', function () {
    $response = $this->postJson('/api/activities', $this->validActivityData);

    $response->assertStatus(401);
});

// Show - Get Single Activity Tests

it('returns activity for authenticated user', function () {
    Sanctum::actingAs($this->user);

    $activity = Activity::factory()->create(['user_id' => $this->user->id]);

    $response = $this->getJson("/api/activities/{$activity->id}");

    $response->assertStatus(200)
        ->assertJsonStructure([
            'id',
            'name',
            'route_points',
            'activity_date',
            'activity_time',
            'duration',
            'pace',
            'activity_type',
            'formatted_pace',
            'created_at',
            'updated_at'
        ]);

    expect($response->json('id'))->toBe($activity->id);
});

it('returns 404 for non-existent activity', function () {
    Sanctum::actingAs($this->user);

    $response = $this->getJson('/api/activities/99999');

    $response->assertStatus(404);
});

it('returns 404 for other users activity', function () {
    Sanctum::actingAs($this->user);

    $otherActivity = Activity::factory()->create(['user_id' => $this->otherUser->id]);

    $response = $this->getJson("/api/activities/{$otherActivity->id}");

    $response->assertStatus(404);
});

it('requires authentication for show', function () {
    $activity = Activity::factory()->create();

    $response = $this->getJson("/api/activities/{$activity->id}");

    $response->assertStatus(401);
});

// Update - Modify Activity Tests

it('updates activity with valid data', function () {
    Sanctum::actingAs($this->user);

    $activity = Activity::factory()->create(['user_id' => $this->user->id]);

    $updateData = [
        'name' => 'Updated Run Name',
        'pace' => 6.0,
        'activity_type' => 'cycling'
    ];

    $response = $this->putJson("/api/activities/{$activity->id}", $updateData);

    $response->assertStatus(200)
        ->assertJsonStructure([
            'message',
            'activity'
        ]);

    $activity->refresh();
    expect($activity->name)->toBe('Updated Run Name');
    expect($activity->pace)->toBe(6.0);
    expect($activity->activity_type)->toBe('cycling');
});

it('validates update data', function () {
    Sanctum::actingAs($this->user);

    $activity = Activity::factory()->create(['user_id' => $this->user->id]);

    $invalidData = [
        'name' => str_repeat('a', 256),
        'duration' => 'invalid',
        'pace' => 'invalid'
    ];

    $response = $this->putJson("/api/activities/{$activity->id}", $invalidData);

    $response->assertStatus(422)
        ->assertJsonValidationErrors(['name', 'duration', 'pace']);
});

it('returns 404 for non-existent activity on update', function () {
    Sanctum::actingAs($this->user);

    $response = $this->putJson('/api/activities/99999', ['name' => 'Test']);

    $response->assertStatus(404);
});

it('returns 404 for other users activity on update', function () {
    Sanctum::actingAs($this->user);

    $otherActivity = Activity::factory()->create(['user_id' => $this->otherUser->id]);

    $response = $this->putJson("/api/activities/{$otherActivity->id}", ['name' => 'Test']);

    $response->assertStatus(404);
});

it('requires authentication for update', function () {
    $activity = Activity::factory()->create();

    $response = $this->putJson("/api/activities/{$activity->id}", ['name' => 'Test']);

    $response->assertStatus(401);
});

// Destroy - Delete Activity Tests

it('deletes activity for authenticated user', function () {
    Sanctum::actingAs($this->user);

    $activity = Activity::factory()->create(['user_id' => $this->user->id]);

    $response = $this->deleteJson("/api/activities/{$activity->id}");

    $response->assertStatus(200)
        ->assertJson([
            'message' => 'Activity deleted successfully'
        ]);

    $this->assertDatabaseMissing('activities', ['id' => $activity->id]);
});

it('returns 404 for non-existent activity on delete', function () {
    Sanctum::actingAs($this->user);

    $response = $this->deleteJson('/api/activities/99999');

    $response->assertStatus(404);
});

it('returns 404 for other users activity on delete', function () {
    Sanctum::actingAs($this->user);

    $otherActivity = Activity::factory()->create(['user_id' => $this->otherUser->id]);

    $response = $this->deleteJson("/api/activities/{$otherActivity->id}");

    $response->assertStatus(404);
});

it('requires authentication for delete', function () {
    $activity = Activity::factory()->create();

    $response = $this->deleteJson("/api/activities/{$activity->id}");

    $response->assertStatus(401);
});

// Activity Model Behavior Tests

it('includes formatted pace in JSON output', function () {
    Sanctum::actingAs($this->user);

    $activity = Activity::factory()->create([
        'user_id' => $this->user->id,
        'pace' => 5.5
    ]);

    $response = $this->getJson("/api/activities/{$activity->id}");

    $response->assertStatus(200)
        ->assertJsonFragment([
            'pace' => 5.5,
            'formatted_pace' => '5:30 min/km'
        ]);
});

it('casts route_points to array', function () {
    Sanctum::actingAs($this->user);

    $routePoints = [[37.7749, -122.4194], [37.7750, -122.4195]];

    $activity = Activity::factory()->create([
        'user_id' => $this->user->id,
        'route_points' => $routePoints
    ]);

    $response = $this->getJson("/api/activities/{$activity->id}");

    $response->assertStatus(200);
    expect($response->json('route_points'))->toEqual($routePoints);
});

it('casts activity_date to date', function () {
    Sanctum::actingAs($this->user);

    $activity = Activity::factory()->create([
        'user_id' => $this->user->id,
        'activity_date' => '2024-01-15'
    ]);

    $response = $this->getJson("/api/activities/{$activity->id}");

    $response->assertStatus(200);
    expect($response->json('activity_date'))->toBe('2024-01-15');
});
