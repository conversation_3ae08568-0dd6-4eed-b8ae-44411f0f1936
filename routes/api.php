<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ActivityExportController;
use App\Http\Controllers\ActivityController;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Authentication Routes
Route::post('/register', [RegisterController::class, 'register']);
Route::post('/login', [LoginController::class, 'login']);

// Protected Routes
Route::middleware('auth:sanctum')->group(function () {
    // User info
    Route::get('/user', function (Request $request) {
        return $request->user();
    });

    // Logout
    Route::post('/logout', [LoginController::class, 'logout']);

    // Activities
    Route::apiResource('activities', ActivityController::class);

    // Export (authenticated)
    Route::post('/export/gpx', [ActivityExportController::class, 'generateGpx']);

    // Exported Activities
    Route::get('/exported-activities', [App\Http\Controllers\ExportedActivityController::class, 'index']);
    Route::get('/exported-activities/{id}', [App\Http\Controllers\ExportedActivityController::class, 'show']);
    Route::get('/exported-activities/{id}/download', [App\Http\Controllers\ExportedActivityController::class, 'download']);

    // Tokens
    Route::get('/tokens', [App\Http\Controllers\TokenController::class, 'getTokens']);
    Route::post('/tokens', [App\Http\Controllers\TokenController::class, 'addTokens']);
    Route::post('/use-token', [App\Http\Controllers\TokenController::class, 'useToken']);

    // Exported Activities Log
    Route::post('/exported-activities', [App\Http\Controllers\ExportedActivityController::class, 'store']);
});

// Public export route (can be accessed without authentication if needed)
Route::post('/export/gpx', [ActivityExportController::class, 'generateGpx']);
